using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    public class AlarmStatusToBrush : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "活跃" => new SolidColorBrush(Color.Parse("#1A0A0A")),  // 活跃报警：深红色背景
                    "已确认" => new SolidColorBrush(Color.Parse("#1A1A0A")), // 已确认报警：深黄色背景
                    "已解除" => new SolidColorBrush(Color.Parse("#0A1A0A")), // 已解除报警：深绿色背景
                    "已清除" => new SolidColorBrush(Color.Parse("#0A1A0A")), // 已清除报警：同样使用深绿色背景
                    _ => new SolidColorBrush(Color.Parse("#161A23"))        // 默认深灰色背景
                };
            }
            
            return new SolidColorBrush(Color.Parse("#161A23"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 