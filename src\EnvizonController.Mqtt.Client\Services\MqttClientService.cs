using EnvizonController.Mqtt.Client.Events;
using EnvizonController.Mqtt.Client.Factories;
using Microsoft.Extensions.Logging;
using MQTTnet.Client;
using MQTTnet;
using MQTTnet.Protocol;

namespace EnvizonController.Mqtt.Client.Services
{
    public class MqttClientService : IMqttClientService, IDisposable
    {
        private readonly IMqttClient _mqttClient;
        private readonly MqttClientOptions _options;
        private readonly IMessageHandlerRegistry _handlerRegistry;
        private readonly ILogger<MqttClientService> _logger;
        private bool _isConnected;
        private readonly List<string> _subscribedTopics = new();

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<MqttConnectionStatusEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        public bool IsConnected => _isConnected;

        public MqttClientService(
            IMqttClientFactory mqttClientFactory, 
            MqttClientOptions options,
            IMessageHandlerRegistry handlerRegistry,
            ILogger<MqttClientService> logger)
        {
            _mqttClient = mqttClientFactory.CreateClient();
            _options = options;
            _handlerRegistry = handlerRegistry;
            _logger = logger;

            _mqttClient.ApplicationMessageReceivedAsync += HandleApplicationMessageReceivedAsync;
            _mqttClient.DisconnectedAsync += HandleDisconnectedAsync;
            _mqttClient.ConnectedAsync += HandleConnectedAsync;
        }

        /// <summary>
        /// 触发连接状态变化事件
        /// </summary>
        /// <param name="isConnected">连接状态</param>
        /// <param name="message">状态信息</param>
        /// <param name="exception">异常（如果有）</param>
        private void OnConnectionStatusChanged(bool isConnected, string message, Exception exception = null)
        {
            _isConnected = isConnected;
            
            var args = new MqttConnectionStatusEventArgs
            {
                IsConnected = isConnected,
                Message = message,
                Exception = exception
            };
            
            ConnectionStatusChanged?.Invoke(this, args);
        }

        public async Task ConnectAsync()
        {
            try
            {
                if (_mqttClient.IsConnected)
                {
                    _logger.LogInformation("已连接到MQTT服务器，无需重新连接");
                    return;
                }
                
                var result = await _mqttClient.ConnectAsync(_options);
                
                if (result.ResultCode == MqttClientConnectResultCode.Success)
                {
                    _logger.LogInformation("已连接到MQTT服务器");
                    // 注意：连接成功事件将由HandleConnectedAsync处理
                    
                    // 重新订阅之前的主题
                    foreach (var topic in _subscribedTopics)
                    {
                        await SubscribeAsync(topic);
                    }
                }
                else
                {
                    _logger.LogWarning($"连接MQTT服务器失败: {result.ResultCode}");
                    OnConnectionStatusChanged(false, $"连接失败: {result.ResultCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接MQTT服务器时发生错误");
                OnConnectionStatusChanged(false, "连接错误", ex);
                throw;
            }
        }

        public async Task DisconnectAsync()
        {
            if (_mqttClient.IsConnected)
            {
                await _mqttClient.DisconnectAsync();
                _logger.LogInformation("已断开与MQTT服务器的连接");
                // 注意：断开连接事件将由HandleDisconnectedAsync处理
            }
        }
        
        /// <summary>
        /// 处理MQTT客户端连接成功事件
        /// </summary>
        private Task HandleConnectedAsync(MqttClientConnectedEventArgs args)
        {
            _logger.LogInformation("MQTT客户端已连接");
            OnConnectionStatusChanged(true, "已成功连接到MQTT服务器");
            return Task.CompletedTask;
        }

        public async Task PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试发布消息但未连接到MQTT服务器");
                await ConnectAsync();
            }

            var applicationMessage = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(message)
                .WithQualityOfServiceLevel(qos)
                .WithRetainFlag(false)
                .Build();

            await _mqttClient.PublishAsync(applicationMessage);
            _logger.LogDebug($"已发布消息: Topic={topic}, Message={message}");
        }

        public async Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试订阅主题但未连接到MQTT服务器");
                await ConnectAsync();
            }

            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter(f => f.WithTopic(topic).WithQualityOfServiceLevel(qos))
                .Build();

            await _mqttClient.SubscribeAsync(subscribeOptions);
            
            if (!_subscribedTopics.Contains(topic))
            {
                _subscribedTopics.Add(topic);
            }
            
            _logger.LogInformation($"已订阅主题: {topic}");
        }

        public async Task UnsubscribeAsync(string topic)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试取消订阅主题但未连接到MQTT服务器");
                return;
            }

            var unsubscribeOptions = new MqttClientUnsubscribeOptionsBuilder()
                .WithTopicFilter(topic)
                .Build();

            await _mqttClient.UnsubscribeAsync(unsubscribeOptions);
            _subscribedTopics.Remove(topic);
            _logger.LogInformation($"已取消订阅主题: {topic}");
        }

        private async Task HandleApplicationMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs args)
        {
            try
            {
                var topic = args.ApplicationMessage.Topic;
                var payload = args.ApplicationMessage.PayloadSegment;
                var message = System.Text.Encoding.UTF8.GetString(payload.Array ?? Array.Empty<byte>(), payload.Offset, payload.Count);

                _logger.LogDebug($"收到消息: Topic={topic}, Message={message}");

                var handlers = _handlerRegistry.GetHandlers(topic);
                foreach (var handler in handlers)
                {
                    await handler.HandleMessageAsync(topic, message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理MQTT消息时发生错误");
            }
        }

        private async Task HandleDisconnectedAsync(MqttClientDisconnectedEventArgs args)
        {
            _logger.LogWarning($"与MQTT服务器断开连接: {args.Reason}");
            
            // 触发连接状态变化事件
            OnConnectionStatusChanged(false, $"断开连接: {args.Reason}", args.Exception);

            // 实现重连逻辑
            await Task.Delay(TimeSpan.FromSeconds(5));
            await ConnectAsync();
        }

        public void Dispose()
        {
            _mqttClient?.Dispose();
        }
    }
} 