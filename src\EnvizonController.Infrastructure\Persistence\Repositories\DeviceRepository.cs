using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 设备仓储实现
    /// </summary>
    public class DeviceRepository : Repository<Device, long>, IDeviceRepository
    {
        public DeviceRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        public async Task<Device?> GetByNameAsync(string name)
        {
            return await _dbSet.FirstOrDefaultAsync(d => d.Name == name);
        }

        /// <summary>
        /// 根据协议ID获取设备列表
        /// </summary>
        public async Task<IEnumerable<Device>> GetByProtocolIdAsync(long protocolId)
        {
            return await _dbSet.Where(d => d.ProtocolId == protocolId).ToListAsync();
        }

        /// <summary>
        /// 根据连接类型获取设备列表
        /// </summary>
        public async Task<IEnumerable<Device>> GetByConnectionTypeAsync(string connectionType)
        {
            return await _dbSet.Where(d => d.ConnectionType == connectionType).ToListAsync();
        }

        /// <summary>
        /// 获取所有串口设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetSerialDevicesAsync()
        {
            return await _dbSet.Where(d => d.ConnectionType == "Serial").ToListAsync();
        }

        /// <summary>
        /// 获取所有网络设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetNetworkDevicesAsync()
        {
            return await _dbSet.Where(d => d.ConnectionType == "Network").ToListAsync();
        }
    }
}
