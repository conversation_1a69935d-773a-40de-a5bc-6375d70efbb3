using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    public class BoolToHighlightBrush : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected 
                    ? new SolidColorBrush(Color.Parse("#224F5F")) 
                    : new SolidColorBrush(Color.Parse("#1A2A3A"));
            }
            
            return new SolidColorBrush(Color.Parse("#1A2A3A"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 