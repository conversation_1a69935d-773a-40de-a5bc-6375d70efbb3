﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
	</PropertyGroup>


	<ItemGroup>
		<Compile Remove="ViewModels\DashboardViewModel2.cs" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Assets\avalonia-logo.ico" />
		<None Remove="Assets\Fonts\FontAwesome6Free-Solid-900.otf" />
		<None Remove="Assets\Fonts\Orbitron-Regular.ttf" />
		<None Remove="Assets\Fonts\Rajdhani-Regular-2.ttf" />
		<None Remove="Assets\Fonts\SansSerif.ttf" />
		<None Remove="Assets\logo.png" />
	</ItemGroup>

	<ItemGroup>
		<AvaloniaResource Include="Assets\avalonia-logo.ico" />
		<AvaloniaResource Include="Assets\Fonts\FontAwesome6Free-Solid-900.otf" />
		<AvaloniaResource Include="Assets\Fonts\Orbitron-Regular.ttf" />
		<AvaloniaResource Include="Assets\Fonts\Rajdhani-Regular-2.ttf" />
		<AvaloniaResource Include="Assets\Fonts\SansSerif.ttf" />
		<AvaloniaResource Include="Assets\logo.png" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Avalonia" Version="$(AvaloniaVersion)" />
		<PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.0" />
		<PackageReference Include="Avalonia.Themes.Fluent" Version="$(AvaloniaVersion)" />
		<PackageReference Include="Avalonia.Fonts.Inter" Version="$(AvaloniaVersion)" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="MessageBox.Avalonia" Version="3.2.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\avalonia-aot-chart\AvaloniaLineSeriesChart\AvaloniaLineSeriesChart.csproj" />
		<ProjectReference Include="..\..\..\avalonia-uikit\src\AvaloniaUiKit\AvaloniaUiKit.csproj" />
		<ProjectReference Include="..\EnvizonController.ApiClient\EnvizonController.ApiClient.csproj" />
		<ProjectReference Include="..\EnvizonController.Configuration\EnvizonController.Configuration.csproj" />
		<ProjectReference Include="..\EnvizonController.Mqtt.Client\EnvizonController.Mqtt.Client.csproj" />
		<ProjectReference Include="..\EnvizonController.Shared\EnvizonController.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Views\Dashboard\DeviceLifetimeControl.axaml.cs">
	    <DependentUpon>DeviceLifetimeControl.axaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Program\ProgramLinkStepsView.axaml.cs">
	    <DependentUpon>ProgramLinkStepsView.axaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\" />
	</ItemGroup>
</Project>
