using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 布尔值转颜色转换器
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not bool boolValue)
                return new SolidColorBrush(Colors.Gray);

            if (parameter is not string colorParams)
                return new SolidColorBrush(boolValue ? Colors.Green : Colors.Red);

            var colors = colorParams.Split(',');
            if (colors.Length != 2)
                return new SolidColorBrush(boolValue ? Colors.Green : Colors.Red);

            try
            {
                return new SolidColorBrush(boolValue ? Color.Parse(colors[0]) : Color.Parse(colors[1]));
            }
            catch
            {
                return new SolidColorBrush(boolValue ? Colors.Green : Colors.Red);
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
