{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "EnvizonController": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "EnvizonController": "Debug"}}}, "SwaggerUI": {"Enabled": true, "RoutePrefix": "", "DocumentTitle": "EnvizonController API Documentation (Development)", "EnableTryItOut": true, "DisplayRequestDuration": true, "ShowExtensions": true}}