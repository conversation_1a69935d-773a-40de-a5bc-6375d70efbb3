using EnvizonController.Domain.Aggregates;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 程式链接仓储接口
    /// </summary>
    public interface IProgramLinkRepository : IRepository<ProgramLink, long>
    {
        /// <summary>
        /// 根据名称获取程式链接
        /// </summary>
        /// <param name="name">程式链接名称</param>
        /// <returns>程式链接</returns>
        Task<ProgramLink> GetByNameAsync(string name);

        /// <summary>
        /// 根据ID获取程式链接，包含链接项
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>程式链接</returns>
        Task<ProgramLink> GetByIdWithItemsAsync(long id);

        /// <summary>
        /// 根据ID获取程式链接，包含链接项及其关联程式的完整步骤信息
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>程式链接</returns>
        Task<ProgramLink> GetByIdWithItemsAndProgramStepsAsync(long id);

        /// <summary>
        /// 获取所有程式链接及其包含的链接项集合。
        /// </summary>
        /// <returns>包含所有程式链接及其链接项的集合</returns>
        Task<IEnumerable<ProgramLink>> GetAllWithItemsAsync();

    }
} 