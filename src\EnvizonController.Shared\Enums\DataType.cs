﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Shared.Enums
{
    /// <summary>
    /// 数据类型枚举
    /// </summary>
    public enum DataType
    {
        /// <summary>
        /// 布尔值
        /// </summary>
        Bool,

        /// <summary>
        /// 8位无符号整数
        /// </summary>
        Byte,

        /// <summary>
        /// 16位有符号整数
        /// </summary>
        Int16,

        /// <summary>
        /// 16位无符号整数
        /// </summary>
        UInt16,

        /// <summary>
        /// 32位有符号整数
        /// </summary>
        Int32,

        /// <summary>
        /// 32位无符号整数
        /// </summary>
        UInt32,

        /// <summary>
        /// 64位有符号整数
        /// </summary>
        Int64,

        /// <summary>
        /// 64位无符号整数
        /// </summary>
        UInt64,

        /// <summary>
        /// 32位浮点数
        /// </summary>
        Float,

        /// <summary>
        /// 64位浮点数
        /// </summary>
        Double,

        /// <summary>
        /// 字符串
        /// </summary>
        String,

        /// <summary>
        /// 字节数组
        /// </summary>
        ByteArray,
        /// <summary>
        /// 枚举类型 具体含义在 ValueMapping 中定义。
        /// </summary>
        Enum,
        /// <summary>
        /// 16位寄存器 每一位有特定含义，在 BitMapping 中定义。
        /// </summary>
        BitField16,
        /// <summary>
        /// 32位寄存器 每一位有特定含义，在 BitMapping 中定义。
        /// </summary>
        BitField32
    }
}
