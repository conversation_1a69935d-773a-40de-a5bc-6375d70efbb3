using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Enums;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.Interfaces
{
    /// <summary>
    /// 设备应用服务接口
    /// </summary>
    public interface IDeviceAppService
    {
        /// <summary>
        /// 获取所有自动启动设备，并为每个设备赋值协议（不保存到数据库）
        /// </summary>
        Task<IEnumerable<Device>> GetAllAutoStartDevicesWithProtocolAsync();
        /// <summary>
        /// 获取所有设备
        /// </summary>
        Task<IEnumerable<Device>> GetAllDevicesAsync();

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        Task<Device?> GetDeviceByIdAsync(long id);

        /// <summary>
        /// 创建设备
        /// </summary>
        Task<Device> CreateDeviceAsync(Device device);

        /// <summary>
        /// 更新设备
        /// </summary>
        Task<Device> UpdateDeviceAsync(Device device);

        /// <summary>
        /// 删除设备
        /// </summary>
        Task DeleteDeviceAsync(long id);

        /// <summary>
        /// 初始化默认设备（如果不存在）
        /// </summary>
        Task<Device?> InitializeDefaultDeviceAsync();

        /// <summary>
        /// 启动设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="testId">测试ID（可选）</param>
        /// <returns>更新后的设备</returns>
        Task<Device?> StartDeviceAsync(long id, long? testId = null);

        /// <summary>
        /// 停止设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        Task<Device?> StopDeviceAsync(long id);

        /// <summary>
        /// 暂停设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        Task<Device?> PauseDeviceAsync(long id);

        /// <summary>
        /// 继续设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        Task<Device?> ContinueDeviceAsync(long id);
    }
}
