using EnvizonController.Mqtt.Client.Factories;
using EnvizonController.Mqtt.Client.Handlers;
using EnvizonController.Mqtt.Client.Services;
using Microsoft.Extensions.DependencyInjection;
using MQTTnet.Client;

namespace EnvizonController.Mqtt.Client.DependencyInjection
{
    public static class MqttClientServiceExtensions
    {
        public static IServiceCollection AddMqttClient(this IServiceCollection services, Action<MqttClientOptionsBuilder> configureOptions)
        {
            // 注册MQTT客户端工厂
            services.AddSingleton<IMqttClientFactory, MqttClientFactory>();
            
            // 注册MQTT客户端选项
            services.AddSingleton(sp => 
            {
                var optionsBuilder = new MqttClientOptionsBuilder();
                configureOptions(optionsBuilder);
                return optionsBuilder.Build();
            });
            
            // 注册消息处理器注册表
            services.AddSingleton<IMessageHandlerRegistry, MessageHandlerRegistry>();
            
            // 注册MQTT客户端服务
            services.AddSingleton<IMqttClientService, MqttClientService>();
            
            // 注册通知服务
            services.AddSingleton<INotificationService, NotificationService>();
            
            // 注册消息处理器
            //services.AddTransient<IMessageHandler, AlarmMessageHandler>();
            //services.AddTransient<IMessageHandler, DeviceStatusMessageHandler>();
            
            return services;
        }
    }
} 