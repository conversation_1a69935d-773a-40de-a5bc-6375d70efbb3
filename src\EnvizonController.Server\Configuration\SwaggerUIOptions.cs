namespace EnvizonController.Server.Configuration
{
    /// <summary>
    /// SwaggerUI配置选项
    /// </summary>
    public class SwaggerUIOptions
    {
        /// <summary>
        /// 配置节名称
        /// </summary>
        public const string SectionName = "SwaggerUI";

        /// <summary>
        /// 是否启用SwaggerUI
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 路由前缀，空字符串表示根路径
        /// </summary>
        public string RoutePrefix { get; set; } = string.Empty;

        /// <summary>
        /// 文档标题
        /// </summary>
        public string DocumentTitle { get; set; } = "EnvizonController API Documentation";

        /// <summary>
        /// 是否启用"Try it out"功能
        /// </summary>
        public bool EnableTryItOut { get; set; } = true;

        /// <summary>
        /// 是否显示请求持续时间
        /// </summary>
        public bool DisplayRequestDuration { get; set; } = true;

        /// <summary>
        /// 是否显示扩展信息
        /// </summary>
        public bool ShowExtensions { get; set; } = true;
    }
} 