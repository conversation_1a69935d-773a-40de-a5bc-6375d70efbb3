using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.Application.Services;

/// <summary>
/// 测试执行策略工厂实现
/// </summary>
public class TestExecutionStrategyFactory : ITestExecutionStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TestExecutionStrategyFactory> _logger;

    public TestExecutionStrategyFactory(
        IServiceProvider serviceProvider,
        ILogger<TestExecutionStrategyFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 创建测试执行策略
    /// </summary>
    public ITestExecutionStrategy CreateStrategy(string executionType)
    {
        return executionType?.ToLower() switch
        {
            "manual" => new ManualTestExecutionStrategy(_serviceProvider, _logger),
            "program" => new ProgramTestExecutionStrategy(_serviceProvider, _logger),
            "programlink" => new ProgramLinkTestExecutionStrategy(_serviceProvider, _logger),
            "constantvalue" => new ConstantValueTestExecutionStrategy(_serviceProvider, _logger),
            "timed" => new TimedTestExecutionStrategy(_serviceProvider, _logger),
            "custom" => new CustomTestExecutionStrategy(_serviceProvider, _logger),
            _ => new ManualTestExecutionStrategy(_serviceProvider, _logger) // 默认为手动测试
        };
    }
}

/// <summary>
/// 手动测试执行策略
/// </summary>
public class ManualTestExecutionStrategy : ITestExecutionStrategy
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger _logger;

    public ManualTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 创建手动测试运行
    /// </summary>
    public async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        _logger.LogInformation("创建手动测试运行，设备ID: {DeviceId}, 测试名称: {TestName}",
            device.Id, request.TestName);

        // 创建手动测试运行
        var testRun = TestRun.Create(
            name: request.TestName,
            deviceId: device.Id,
            status: "Created",
            description: request.Description ?? "手动测试");

        testRun.ExecutionType = "Manual";
        testRun.ExecutionConfigJson = CreateManualTestConfig(request);

        await Task.CompletedTask; // 手动测试不需要异步操作
        return testRun;
    }

    /// <summary>
    /// 创建手动测试配置
    /// </summary>
    private string CreateManualTestConfig(DeviceTestStartRequest request)
    {
        var config = new
        {
            Type = "Manual",
            TestName = request.TestName,
            Description = request.Description,
            CreatedAt = DateTime.Now,
            ExtraConfig = request.ExtraConfig
        };

        return JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
    }
}

/// <summary>
/// 程式测试执行策略
/// </summary>
public class ProgramTestExecutionStrategy : ITestExecutionStrategy
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger _logger;

    public ProgramTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 创建程式测试运行
    /// </summary>
    public async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        _logger.LogInformation("创建程式测试运行，设备ID: {DeviceId}, 程式ID: {ProgramId}",
            device.Id, request.ExecutionId);

        if (!request.ExecutionId.HasValue)
        {
            throw new InvalidOperationException("程式测试需要指定程式ID");
        }

        // 使用领域服务创建基于程式的测试运行
        using var scope = _serviceProvider.CreateScope();
        var testRunService = scope.ServiceProvider.GetRequiredService<ITestRunDomainService>();

        var testRun = await testRunService.CreateTestFromProgramAsync(
            programId: request.ExecutionId.Value,
            testName: request.TestName,
            deviceId: device.Id,
            description: request.Description ?? "基于程式的测试");

        return testRun;
    }
}

/// <summary>
/// 程式链接测试执行策略
/// </summary>
public class ProgramLinkTestExecutionStrategy : ITestExecutionStrategy
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger _logger;

    public ProgramLinkTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 创建程式链接测试运行
    /// </summary>
    public async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        _logger.LogInformation("创建程式链接测试运行，设备ID: {DeviceId}, 程式链接ID: {ProgramLinkId}",
            device.Id, request.ExecutionId);

        if (!request.ExecutionId.HasValue)
        {
            throw new InvalidOperationException("程式链接测试需要指定程式链接ID");
        }

        // 记录额外配置参数
        if (request.ExtraConfig != null && request.ExtraConfig.Count > 0)
        {
            _logger.LogInformation("程式链接测试包含额外配置参数: {ExtraConfigKeys}",
                string.Join(", ", request.ExtraConfig.Keys));
        }

        // 使用领域服务创建基于程式链接的测试运行，传递额外配置
        using var scope = _serviceProvider.CreateScope();
        var testRunService = scope.ServiceProvider.GetRequiredService<ITestRunDomainService>();

        var testRun = await testRunService.CreateTestFromProgramLinkAsync(
            programLinkId: request.ExecutionId.Value,
            testName: request.TestName,
            deviceId: device.Id,
            description: request.Description ?? "基于程式链接的测试",
            extraConfig: request.ExtraConfig);

        return testRun;
    }
}

/// <summary>
/// 测试执行策略基类 - 可扩展更多策略
/// </summary>
public abstract class BaseTestExecutionStrategy : ITestExecutionStrategy
{
    protected readonly IServiceProvider ServiceProvider;
    protected readonly ILogger Logger;

    protected BaseTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
    {
        ServiceProvider = serviceProvider;
        Logger = logger;
    }

    /// <summary>
    /// 创建测试运行的抽象方法
    /// </summary>
    public abstract Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device);

    /// <summary>
    /// 通用的验证方法
    /// </summary>
    protected virtual void ValidateRequest(DeviceTestStartRequest request, Device device)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (device == null)
            throw new ArgumentNullException(nameof(device));

        if (string.IsNullOrWhiteSpace(request.TestName))
            throw new ArgumentException("测试名称不能为空", nameof(request));
    }

    /// <summary>
    /// 创建基础配置JSON
    /// </summary>
    protected virtual string CreateBaseConfig(DeviceTestStartRequest request, string strategyType)
    {
        var config = new
        {
            Type = strategyType,
            TestName = request.TestName,
            Description = request.Description,
            ExecutionId = request.ExecutionId,
            CreatedAt = DateTime.Now,
            ExtraConfig = request.ExtraConfig
        };

        return JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
    }
}

/// <summary>
/// 定值测试执行策略
/// </summary>
public class ConstantValueTestExecutionStrategy : BaseTestExecutionStrategy
{
    public ConstantValueTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
        : base(serviceProvider, logger)
    {
    }

    public override async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        ValidateRequest(request, device);

        Logger.LogInformation("创建定值测试运行，设备ID: {DeviceId}, 测试名称: {TestName}",
            device.Id, request.TestName);

        // 验证定值模式必需的参数
        ValidateConstantValueParameters(request);

        // 创建定值测试运行
        var testRun = TestRun.Create(
            name: request.TestName,
            deviceId: device.Id,
            status: "Created",
            description: request.Description ?? "定值模式测试");

        testRun.ExecutionType = "ConstantValue";
        testRun.ExecutionConfigJson = CreateBaseConfig(request, "ConstantValue");

        await Task.CompletedTask;
        return testRun;
    }

    /// <summary>
    /// 验证定值模式参数
    /// </summary>
    private void ValidateConstantValueParameters(DeviceTestStartRequest request)
    {
        if (request.ExtraConfig == null)
        {
            throw new ArgumentException("定值模式需要提供配置参数");
        }

        // 验证必需的参数
        if (!request.ExtraConfig.ContainsKey("temperature"))
        {
            throw new ArgumentException("定值模式需要提供温度设定值");
        }

        if (!request.ExtraConfig.ContainsKey("humidity"))
        {
            throw new ArgumentException("定值模式需要提供湿度设定值");
        }

        // 验证参数范围
        if (request.ExtraConfig.TryGetValue("temperature", out var tempValue))
        {
            if (double.TryParse(tempValue?.ToString(), out var temp))
            {
                if (temp < -100 || temp > 200)
                {
                    throw new ArgumentException("温度设定值必须在-100到200之间");
                }
            }
            else
            {
                throw new ArgumentException("温度设定值格式无效");
            }
        }

        if (request.ExtraConfig.TryGetValue("humidity", out var humidityValue))
        {
            if (double.TryParse(humidityValue?.ToString(), out var humidity))
            {
                if (humidity < 0 || humidity > 100)
                {
                    throw new ArgumentException("湿度设定值必须在0到100之间");
                }
            }
            else
            {
                throw new ArgumentException("湿度设定值格式无效");
            }
        }
    }
}

/// <summary>
/// 定时测试执行策略
/// </summary>
public class TimedTestExecutionStrategy : BaseTestExecutionStrategy
{
    public TimedTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
        : base(serviceProvider, logger)
    {
    }

    public override async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        ValidateRequest(request, device);

        Logger.LogInformation("创建定时测试运行，设备ID: {DeviceId}, 测试名称: {TestName}",
            device.Id, request.TestName);

        // 验证定时模式必需的参数
        ValidateTimedParameters(request);

        // 创建定时测试运行
        var testRun = TestRun.Create(
            name: request.TestName,
            deviceId: device.Id,
            status: "Created",
            description: request.Description ?? "定时模式测试");

        testRun.ExecutionType = "Timed";
        testRun.ExecutionConfigJson = CreateBaseConfig(request, "Timed");

        // 计算预计持续时间
        if (request.ExtraConfig != null)
        {
            var estimatedDuration = CalculateTimedDuration(request.ExtraConfig);
            testRun.SetEstimatedDuration(estimatedDuration);
        }

        await Task.CompletedTask;
        return testRun;
    }

    /// <summary>
    /// 验证定时模式参数
    /// </summary>
    private void ValidateTimedParameters(DeviceTestStartRequest request)
    {
        if (request.ExtraConfig == null)
        {
            throw new ArgumentException("定时模式需要提供配置参数");
        }

        // 验证必需的参数
        if (!request.ExtraConfig.ContainsKey("temperature"))
        {
            throw new ArgumentException("定时模式需要提供温度设定值");
        }

        if (!request.ExtraConfig.ContainsKey("humidity"))
        {
            throw new ArgumentException("定时模式需要提供湿度设定值");
        }

        // 验证时间参数
        var hasDays = request.ExtraConfig.ContainsKey("days");
        var hasHours = request.ExtraConfig.ContainsKey("hours");
        var hasMinutes = request.ExtraConfig.ContainsKey("minutes");

        if (!hasDays && !hasHours && !hasMinutes)
        {
            throw new ArgumentException("定时模式需要提供至少一个时间参数（天数、小时数或分钟数）");
        }

        // 验证参数范围
        ValidateTemperatureAndHumidity(request.ExtraConfig);
        ValidateTimeParameters(request.ExtraConfig);
    }

    /// <summary>
    /// 验证温度和湿度参数
    /// </summary>
    private void ValidateTemperatureAndHumidity(Dictionary<string, object> extraConfig)
    {
        if (extraConfig.TryGetValue("temperature", out var tempValue))
        {
            if (double.TryParse(tempValue?.ToString(), out var temp))
            {
                if (temp < -100 || temp > 200)
                {
                    throw new ArgumentException("温度设定值必须在-100到200之间");
                }
            }
            else
            {
                throw new ArgumentException("温度设定值格式无效");
            }
        }

        if (extraConfig.TryGetValue("humidity", out var humidityValue))
        {
            if (double.TryParse(humidityValue?.ToString(), out var humidity))
            {
                if (humidity < 0 || humidity > 100)
                {
                    throw new ArgumentException("湿度设定值必须在0到100之间");
                }
            }
            else
            {
                throw new ArgumentException("湿度设定值格式无效");
            }
        }
    }

    /// <summary>
    /// 验证时间参数
    /// </summary>
    private void ValidateTimeParameters(Dictionary<string, object> extraConfig)
    {
        if (extraConfig.TryGetValue("days", out var daysValue))
        {
            if (int.TryParse(daysValue?.ToString(), out var days))
            {
                if (days < 0 || days > 365)
                {
                    throw new ArgumentException("天数必须在0到365之间");
                }
            }
            else
            {
                throw new ArgumentException("天数格式无效");
            }
        }

        if (extraConfig.TryGetValue("hours", out var hoursValue))
        {
            if (int.TryParse(hoursValue?.ToString(), out var hours))
            {
                if (hours < 0 || hours > 23)
                {
                    throw new ArgumentException("小时数必须在0到23之间");
                }
            }
            else
            {
                throw new ArgumentException("小时数格式无效");
            }
        }

        if (extraConfig.TryGetValue("minutes", out var minutesValue))
        {
            if (int.TryParse(minutesValue?.ToString(), out var minutes))
            {
                if (minutes < 0 || minutes > 59)
                {
                    throw new ArgumentException("分钟数必须在0到59之间");
                }
            }
            else
            {
                throw new ArgumentException("分钟数格式无效");
            }
        }
    }

    /// <summary>
    /// 计算定时测试的预计持续时间（秒）
    /// </summary>
    private int CalculateTimedDuration(Dictionary<string, object> extraConfig)
    {
        var totalSeconds = 0;

        if (extraConfig.TryGetValue("days", out var daysValue) && int.TryParse(daysValue?.ToString(), out var days))
        {
            totalSeconds += days * 24 * 60 * 60;
        }

        if (extraConfig.TryGetValue("hours", out var hoursValue) && int.TryParse(hoursValue?.ToString(), out var hours))
        {
            totalSeconds += hours * 60 * 60;
        }

        if (extraConfig.TryGetValue("minutes", out var minutesValue) && int.TryParse(minutesValue?.ToString(), out var minutes))
        {
            totalSeconds += minutes * 60;
        }

        return totalSeconds;
    }
}

/// <summary>
/// 自定义测试执行策略 - 演示如何扩展新的策略
/// </summary>
public class CustomTestExecutionStrategy : BaseTestExecutionStrategy
{
    public CustomTestExecutionStrategy(IServiceProvider serviceProvider, ILogger logger)
        : base(serviceProvider, logger)
    {
    }

    public override async Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device)
    {
        ValidateRequest(request, device);

        Logger.LogInformation("创建自定义测试运行，设备ID: {DeviceId}, 测试名称: {TestName}",
            device.Id, request.TestName);

        // 实现自定义测试逻辑
        var testRun = TestRun.Create(
            name: request.TestName,
            deviceId: device.Id,
            status: "Created",
            description: request.Description ?? "自定义测试");

        testRun.ExecutionType = "Custom";
        testRun.ExecutionConfigJson = CreateBaseConfig(request, "Custom");

        // 可以在此处添加更多自定义逻辑
        // 比如加载特定的配置、验证参数等

        await Task.CompletedTask;
        return testRun;
    }
}