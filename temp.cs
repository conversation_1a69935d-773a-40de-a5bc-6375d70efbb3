using EnvizonController.Application.Devices;

// 测试ModbusConnectionType的定义
public class Test
{
    public void TestMethod()
    {
        // 使用ModbusConnectionType枚举
        ModbusConnectionType connectionType = ModbusConnectionType.Serial;
        
        // 打印所有可能的值
        foreach (ModbusConnectionType type in Enum.GetValues(typeof(ModbusConnectionType)))
        {
            Console.WriteLine(type);
        }
    }
}
