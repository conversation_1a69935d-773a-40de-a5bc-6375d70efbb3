using System;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Mqtt.Client.Events;
using EnvizonController.Mqtt.Client.Services;
using EnvizonController.Presentation.Messages;
using EnvizonController.Shared.Enums;
using MQTTnet.Client;
using Serilog;

namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接管理器实现，负责管理MQTT连接的生命周期和状态
    /// </summary>
    public class MqttConnectionManager : IMqttConnectionManager, IDisposable
    {
        private readonly IMqttClientService _mqttClient;
        private readonly IMessageHandlerRegistry _handlerRegistry;
        private readonly ILogger _logger;
        private readonly IMessenger _messenger;
        private ConnectionStatus _status;
        private CancellationTokenSource _reconnectCts;
        private readonly MqttSettings _mqttSettings;
        private bool _disposed = false;

        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        public ConnectionStatus Status => _status;

        /// <summary>
        /// 当连接状态变化时发生
        /// </summary>
        public event EventHandler<MqttConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 初始化 <see cref="MqttConnectionManager"/> 类的新实例
        /// </summary>
        /// <param name="mqttClient">MQTT客户端服务</param>
        /// <param name="handlerRegistry">消息处理器注册表</param>
        /// <param name="messenger">消息发送器</param>
        /// <param name="logger">日志记录器</param>
        public MqttConnectionManager(
            IMqttClientService mqttClient,
            IMessageHandlerRegistry handlerRegistry,
            IMessenger messenger,
            ILogger logger)
        {
            _mqttClient = mqttClient ?? throw new ArgumentNullException(nameof(mqttClient));
            _handlerRegistry = handlerRegistry ?? throw new ArgumentNullException(nameof(handlerRegistry));
            _mqttSettings = AppConfigurationProvider.ConfigurationAsyncLoaded.MqttSettings;
            _messenger = messenger ?? throw new ArgumentNullException(nameof(messenger));
            _logger = logger?.ForContext<MqttConnectionManager>() ?? throw new ArgumentNullException(nameof(logger));
            _status = ConnectionStatus.Disconnected;

            // 订阅MQTT客户端的连接状态变化事件
            _mqttClient.ConnectionStatusChanged += OnMqttClientConnectionStatusChanged;
        }

        /// <summary>
        /// 处理MQTT客户端连接状态变化事件
        /// </summary>
        private void OnMqttClientConnectionStatusChanged(object sender, MqttConnectionStatusEventArgs e)
        {
            if (e.IsConnected)
            {
                if (_status == ConnectionStatus.Reconnecting)
                {
                    StopReconnectionLoop();
                    UpdateStatus(ConnectionStatus.Connected, e.Message ?? "重新连接成功");
                }
                else if (_status != ConnectionStatus.Connected)
                {
                    UpdateStatus(ConnectionStatus.Connected, e.Message ?? "已成功连接到MQTT服务器");
                }
            }
            else
            {
                // 如果连接断开且当前状态为已连接
                if (_status == ConnectionStatus.Connected)
                {
                    _logger.Warning("检测到MQTT连接已断开");
                    
                    // 如果启用了自动重连，开始重连过程
                    if (_mqttSettings.AutoReconnect)
                    {
                        UpdateStatus(ConnectionStatus.Reconnecting, e.Message ?? "连接断开，正在尝试重新连接");
                        StartReconnectionLoop();
                    }
                    else
                    {
                        UpdateStatus(ConnectionStatus.Disconnected, e.Message ?? "连接已断开且自动重连已禁用");
                    }
                }
                else if (_status == ConnectionStatus.Connecting)
                {
                    UpdateStatus(ConnectionStatus.Error, e.Message ?? "连接失败", e.Exception);
                    
                    // 如果启用了自动重连，开始重连过程
                    if (_mqttSettings.AutoReconnect)
                    {
                        StartReconnectionLoop();
                    }
                }
            }
        }

        /// <summary>
        /// 异步连接到MQTT服务器
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task ConnectAsync()
        {
            if (_status == ConnectionStatus.Connected || 
                _status == ConnectionStatus.Connecting ||
                _status == ConnectionStatus.Reconnecting)
            {
                _logger.Debug("已连接或正在连接到MQTT服务器，忽略连接请求");
                return;
            }

            try
            {
                UpdateStatus(ConnectionStatus.Connecting, "正在连接到MQTT服务器...");
                
                await _mqttClient.ConnectAsync();
                
                // 注意：连接结果将由OnMqttClientConnectionStatusChanged处理
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接MQTT服务器失败");
                UpdateStatus(ConnectionStatus.Error, $"连接失败: {ex.Message}", ex);
                
                // 如果启用了自动重连，开始重连过程
                if (_mqttSettings.AutoReconnect)
                {
                    StartReconnectionLoop();
                }
            }
        }

        /// <summary>
        /// 异步断开与MQTT服务器的连接
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task DisconnectAsync()
        {
            // 停止任何正在进行的重连尝试
            StopReconnectionLoop();
            
            if (_status == ConnectionStatus.Disconnected)
            {
                return;
            }

            try
            {
                await _mqttClient.DisconnectAsync();
                UpdateStatus(ConnectionStatus.Disconnected, "已断开与MQTT服务器的连接");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开MQTT连接时发生错误");
                // 即使出现异常，我们也将状态设置为已断开
                UpdateStatus(ConnectionStatus.Disconnected, "断开连接过程中发生错误，但已断开");
            }
        }

        /// <summary>
        /// 更新连接状态并触发相关事件
        /// </summary>
        /// <param name="newStatus">新状态</param>
        /// <param name="message">状态描述信息</param>
        /// <param name="error">相关异常（如果有）</param>
        private void UpdateStatus(ConnectionStatus newStatus, string message, Exception error = null)
        {
            var oldStatus = _status;
            _status = newStatus;
            
            var args = new MqttConnectionStatusChangedEventArgs
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message,
                Error = error
            };
            
            // 触发ConnectionStatusChanged事件
            ConnectionStatusChanged?.Invoke(this, args);
            
            // 发布状态变更消息，以便UI和其他组件能够接收
            _messenger.Send(new MqttConnectionStatusChangedMessage
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message,
                Error = error
            });

            _logger.Information($"MQTT连接状态已变更: {oldStatus} -> {newStatus}, 信息: {message}");
        }

        /// <summary>
        /// 启动重连循环
        /// </summary>
        private void StartReconnectionLoop()
        {
            StopReconnectionLoop();
            
            _reconnectCts = new CancellationTokenSource();
            
            // 在后台线程中启动重连循环，避免阻塞UI
            Task.Run(async () => await ReconnectionLoopAsync(_reconnectCts.Token));
        }

        /// <summary>
        /// 停止重连循环
        /// </summary>
        private void StopReconnectionLoop()
        {
            if (_reconnectCts != null)
            {
                _reconnectCts.Cancel();
                _reconnectCts.Dispose();
                _reconnectCts = null;
            }
        }

        /// <summary>
        /// 重连循环逻辑
        /// </summary>
        private async Task ReconnectionLoopAsync(CancellationToken cancellationToken)
        {
            int attempts = 0;
            bool isReconnected = false;
            
            while (!isReconnected && !cancellationToken.IsCancellationRequested && attempts < _mqttSettings.MaxAttempts)
            {
                attempts++;
                
                // 计算延迟时间（使用退避策略）
                int delayMs = _mqttSettings.CalculateDelay(attempts);
                
                _logger.Information($"正在等待重新连接，尝试 {attempts}/{_mqttSettings.MaxAttempts}，延迟 {delayMs}ms");
                
                // 等待指定的延迟时间
                await Task.Delay(delayMs, cancellationToken);
                
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                try
                {
                    _logger.Information($"尝试重新连接到MQTT服务器 (尝试 {attempts}/{_mqttSettings.MaxAttempts})");
                    
                    // 尝试重新连接
                    await _mqttClient.ConnectAsync();
                    
                    // 如果成功连接，退出重连循环
                    if (_mqttClient.IsConnected)
                    {
                        isReconnected = true;
                        _logger.Information("已成功重新连接到MQTT服务器");
                        // 注意：状态更新将由连接状态变化事件处理
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"重新连接MQTT服务器失败 (尝试 {attempts}/{_mqttSettings.MaxAttempts})");
                }
            }
            
            // 如果达到最大尝试次数仍未连接成功
            if (!isReconnected && !cancellationToken.IsCancellationRequested)
            {
                _logger.Warning($"达到最大重连尝试次数 ({_mqttSettings.MaxAttempts})，停止重连");
                UpdateStatus(ConnectionStatus.Error, $"重连失败：已达最大尝试次数 ({_mqttSettings.MaxAttempts})");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;
                
            _disposed = true;
            
            // 取消订阅事件
            if (_mqttClient != null)
            {
                _mqttClient.ConnectionStatusChanged -= OnMqttClientConnectionStatusChanged;
            }
            
            // 停止重连循环
            StopReconnectionLoop();
        }
    }
}