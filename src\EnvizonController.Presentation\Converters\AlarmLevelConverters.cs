using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 报警级别转换为字符串的转换器
    /// </summary>
    public class AlarmLevelToStringConverter : IMultiValueConverter
    {
        public object Convert(IList<object> values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Count == 0 || values[0] is not AlarmLevel level)
                return "未知";

            return level switch
            {
                AlarmLevel.Notice => "信息",
                AlarmLevel.Warning => "警告",
                AlarmLevel.Critical => "严重",
                _ => "未知"
            };
        }
    }

    /// <summary>
    /// 报警级别转换为画刷的转换器
    /// </summary>
    public class AlarmLevelToBrushConverter : IMultiValueConverter
    {
        public object Convert(IList<object> values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Count == 0 || values[0] is not AlarmLevel level)
                return new SolidColorBrush(Colors.Gray);

            return level switch
            {
                AlarmLevel.Notice => new SolidColorBrush(Color.Parse("#3498db")),
                AlarmLevel.Warning => new SolidColorBrush(Color.Parse("#f39c12")),
                AlarmLevel.Critical => new SolidColorBrush(Color.Parse("#e74c3c")),
                _ => new SolidColorBrush(Colors.Gray)
            };
        }
    }
}
