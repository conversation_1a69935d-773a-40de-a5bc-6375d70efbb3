using Avalonia.Data.Converters;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将设备运行状态转换为按钮可见性
    /// </summary>
    public class DeviceStatusToButtonVisibilityConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not DeviceOperatingStatus status || parameter is not string buttonType)
                return true;

            return buttonType.ToLowerInvariant() switch
            {
                "start" => status == DeviceOperatingStatus.Stopped,
                "pause" => status == DeviceOperatingStatus.Running,
                "resume" => status == DeviceOperatingStatus.Paused,
                "stop" => status == DeviceOperatingStatus.Running || status == DeviceOperatingStatus.Paused,
                _ => true
            };
        }
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
}
