﻿// EnvizonController.Presentation/Converters/ValueConverters.cs
using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Converters
{
    public class PowerStateTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isPowered)
            {
                return isPowered ? "已上电" : "已断电";
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class PowerStateBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isPowered)
            {
                return isPowered ? new SolidColorBrush(Color.Parse("#2ecc71")) : new SolidColorBrush(Color.Parse("#95a5a6"));
            }
            return new SolidColorBrush(Color.Parse("#95a5a6"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class AlarmBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool hasAlarm)
            {
                return hasAlarm ? new SolidColorBrush(Color.Parse("#fff0f0")) : new SolidColorBrush(Color.Parse("White"));
            }
            return new SolidColorBrush(Color.Parse("White"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class EventTypeBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is EventType eventType)
            {
                return eventType switch
                {
                    EventType.Information => new SolidColorBrush(Color.Parse("#e8f4fd")),
                    EventType.StateChange => new SolidColorBrush(Color.Parse("#e8fdea")),
                    EventType.Warning => new SolidColorBrush(Color.Parse("#fcf3cf")),
                    EventType.Error => new SolidColorBrush(Color.Parse("#f9ebeb")),
                    EventType.AlarmTriggered => new SolidColorBrush(Color.Parse("#fadbd8")),
                    EventType.AlarmCleared => new SolidColorBrush(Color.Parse("#d4efdf")),
                    EventType.ConnectionChange => new SolidColorBrush(Color.Parse("#ebdef0")),
                    _ => new SolidColorBrush(Color.Parse("#f5f5f5"))
                };
            }
            return new SolidColorBrush(Color.Parse("#f5f5f5"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}