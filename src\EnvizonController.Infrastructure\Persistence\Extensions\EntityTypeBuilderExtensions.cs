using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace EnvizonController.Infrastructure.Persistence.Extensions
{
    /// <summary>
    /// 实体类型构建器扩展方法
    /// </summary>
    public static class EntityTypeBuilderExtensions
    {
        /// <summary>
        /// 将属性配置为JSON转换
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <typeparam name="TProperty">属性类型</typeparam>
        /// <param name="propertyBuilder">属性构建器</param>
        /// <returns>属性构建器</returns>
        public static PropertyBuilder<TProperty> HasJsonConversion<TEntity, TProperty>(
            this PropertyBuilder<TProperty> propertyBuilder)
            where TEntity : class
        {
            return propertyBuilder.HasConversion(
                v => JsonSerializer.Serialize(v, new JsonSerializerOptions { WriteIndented = false }),
                v => JsonSerializer.Deserialize<TProperty>(v, new JsonSerializerOptions { WriteIndented = false })!);
        }
    }
}
