using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;

namespace EnvizonController.Modbus.Abstractions.Clients
{
    /// <summary>
    /// Modbus客户端基类
    /// </summary>
    public abstract class ModbusClientBase : IModbusClient
    {
        private readonly IModbusChannel _channel;
        private readonly IModbusFrameBuilder _frameBuilder;
        private readonly int _retryCount;
        private readonly int _retryDelayMilliseconds;
        private readonly int _responseTimeout;
        private bool _isDisposed;

        /// <summary>
        /// 获取客户端是否已连接
        /// </summary>
        public bool IsConnected => _channel.IsConnected;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="transportType">传输类型</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        protected ModbusClientBase(
            IModbusChannel channel,
            ModbusTransportType transportType,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
        {
            _channel = channel ?? throw new ArgumentNullException(nameof(channel));
            _frameBuilder = ModbusFrameBuilderFactory.CreateFrameBuilder(transportType);
            _retryCount = retryCount;
            _retryDelayMilliseconds = retryDelayMilliseconds;
            _responseTimeout = responseTimeout;
        }

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public virtual async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _channel.ConnectAsync(cancellationToken);
        }

        /// <summary>
        /// 断开与Modbus设备的连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public virtual async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _channel.DisconnectAsync(cancellationToken);
        }
        private readonly SemaphoreSlim _clientSemaphore = new(1, 1);  // 添加客户端级别的信号量

        public virtual async Task<bool> SendRequestAsync(ModbusRequest request, ModbusResponse response, CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (response == null)
                throw new ArgumentNullException(nameof(response));

            if (!IsConnected)
                throw new InvalidOperationException("Modbus客户端未连接");

            // 获取客户端级别的信号量，确保整个"发送-接收"过程的原子性
            await _clientSemaphore.WaitAsync(cancellationToken);
            try
            {
                // 构建请求帧
                byte[] requestFrame = _frameBuilder.BuildRequestFrame(request);

                // 重试逻辑
                for (int attempt = 0; attempt <= _retryCount; attempt++)
                {
                    try
                    {
                        // 清空接收缓冲区
                        await _channel.ClearReceiveBufferAsync();

                        // 发送请求
                        await _channel.SendAsync(requestFrame, cancellationToken);

                        // 接收响应
                        byte[] responseFrame = await _channel.ReceiveAsync(_responseTimeout, cancellationToken);

                        // 解析响应
                        if (_frameBuilder.ParseResponseFrame(responseFrame, response))
                        {
                            // 检查从站地址是否匹配
                            if (response.SlaveAddress != request.SlaveAddress)
                                continue;

                            // 检查功能码是否匹配（考虑异常响应）
                            if (!response.IsException && response.FunctionCode != request.FunctionCode)
                                continue;

                            return true;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception)
                    {
                        // 如果是最后一次尝试，则重新抛出异常
                        if (attempt == _retryCount)
                            throw;
                    }

                    // 延迟后重试
                    if (attempt < _retryCount)
                    {
                        await Task.Delay(_retryDelayMilliseconds, cancellationToken);
                    }
                }

                return false;
            }
            finally
            {
                _clientSemaphore.Release();
            }
        }

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="registerCount">寄存器数量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>寄存器值</returns>
        public virtual async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort registerCount, CancellationToken cancellationToken = default)
        {
            var request = new ReadHoldingRegistersRequest(slaveAddress, startAddress, registerCount);
            var response = new ReadHoldingRegistersResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                throw new InvalidOperationException("读取保持寄存器失败");

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.RegisterValues;
        }

        /// <summary>
        /// 写入多个寄存器
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">寄存器值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        public virtual async Task<bool> WriteMultipleRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values, CancellationToken cancellationToken = default)
        {
            var request = new WriteMultipleRegistersRequest(slaveAddress, startAddress, values);
            var response = new WriteMultipleRegistersResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                return false;

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            // 验证响应中的地址和寄存器数量是否与请求匹配
            return response.StartAddress == startAddress && response.RegisterCount == values.Length;
        }

        public virtual async Task<ushort[]> ReadInputRegistersAsync(byte slaveAddress, ushort startAddress, ushort registerCount, CancellationToken cancellationToken = default)
        {
            var request = new ReadInputRegistersRequest(slaveAddress, startAddress, registerCount);
            var response = new ReadInputRegistersResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                throw new InvalidOperationException("读取输入寄存器失败");

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.RegisterValues;
        }

        public virtual async Task<bool[]> ReadCoilsAsync(byte slaveAddress, ushort startAddress, ushort coilCount, CancellationToken cancellationToken = default)
        {
            var request = new ReadCoilsRequest(slaveAddress, startAddress, coilCount);
            var response = new ReadCoilsResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                throw new InvalidOperationException("读取线圈状态失败");

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.CoilValues;
        }

        public virtual async Task<bool[]> ReadDiscreteInputsAsync(byte slaveAddress, ushort startAddress, ushort inputCount, CancellationToken cancellationToken = default)
        {
            var request = new ReadDiscreteInputsRequest(slaveAddress, startAddress, inputCount);
            var response = new ReadDiscreteInputsResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                throw new InvalidOperationException("读取离散输入状态失败");

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.InputValues;
        }

        public virtual async Task<bool> WriteSingleCoilAsync(byte slaveAddress, ushort address, bool value, CancellationToken cancellationToken = default)
        {
            var request = new WriteSingleCoilRequest(slaveAddress, address, value);
            var response = new WriteSingleCoilResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                return false;

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.Address == address && response.Value == value;
        }

        public virtual async Task<bool> WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value, CancellationToken cancellationToken = default)
        {
            var request = new WriteSingleRegisterRequest(slaveAddress, address, value);
            var response = new WriteSingleRegisterResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                return false;

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.Address == address && response.Value == value;
        }

        public virtual async Task<bool> WriteMultipleCoilsAsync(byte slaveAddress, ushort startAddress, bool[] values, CancellationToken cancellationToken = default)
        {
            var request = new WriteMultipleCoilsRequest(slaveAddress, startAddress, values);
            var response = new WriteMultipleCoilsResponse();

            bool success = await SendRequestAsync(request, response, cancellationToken);

            if (!success)
                return false;

            if (response.IsException)
                throw new InvalidOperationException($"Modbus异常: {response.ExceptionCode}");

            return response.StartAddress == startAddress && response.CoilCount == values.Length;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 释放托管资源
                _channel.Dispose();
                _clientSemaphore.Dispose();  // 释放信号量
            }

            _isDisposed = true;
        }
    }
}
