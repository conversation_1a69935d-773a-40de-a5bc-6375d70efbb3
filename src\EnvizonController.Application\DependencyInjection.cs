using EnvizonController.Application.DataCollection;
using EnvizonController.Application.DataCollection.Strategies;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Mapping;
using EnvizonController.Application.Services;
using Microsoft.Extensions.DependencyInjection;
using AutoMapper;
using System;
using EnvizonController.Application.Interfaces.Services;
using EnvizonController.Application.Mappings;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Services;
using EnvizonController.Application.DeviceCommands.SmartMatching;
using EnvizonController.Application.DeviceCommands.Configuration;

namespace EnvizonController.Application
{
    /// <summary>
    /// 应用层依赖注入扩展方法
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// 添加应用服务
        /// </summary>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // 注册AutoMapper
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                // 添加所有按领域组织的 Profile
                cfg.AddProfile<AlarmMappingProfile>();
                cfg.AddProfile<DeviceMappingProfile>();
                cfg.AddProfile<TestItemMappingProfile>();
                cfg.AddProfile<DataPointMappingProfile>();
                cfg.AddProfile<ProgramMappingProfile>();
                cfg.AddProfile<ProgramLinkMappingProfile>(); 
            });

            // 开发环境下验证配置
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (env == "Development")
            {
                mapperConfig.AssertConfigurationIsValid();
            }

            // 创建 IMapper 实例并注册为单例
            IMapper mapper = mapperConfig.CreateMapper();
            services.AddSingleton(mapper);

            // 注册应用服务
            services.AddScoped<IAlarmAppService, AlarmAppService>();
            services.AddScoped<IProtocolAppService, ProtocolAppService>();
            services.AddScoped<ITestItemAppService, TestItemAppService>();
            services.AddScoped<IDeviceAppService, DeviceAppService>();
            services.AddScoped<IDataCollectionAppService, DataCollectionAppService>();
            services.AddScoped<IProgramAppService, ProgramAppService>();
            services.AddScoped<IProgramLinkAppService, ProgramLinkAppService>();
            
            // 注册新的设备测试服务
            services.AddScoped<IDeviceTestService, DeviceTestService>();
            services.AddScoped<ITestExecutionStrategyFactory, TestExecutionStrategyFactory>();

            // 注册数据采集服务
            services.AddSingleton<DataBufferService>();
            services.AddSingleton<IDataCollectionCoordinator, DataCollectionCoordinator>();
            //services.AddSingleton<IDataCollectionTaskScheduler, DataCollectionTaskScheduler>();

            services.AddSingleton<ICollectionStrategy, AlarmCollectionStrategy>();
            services.AddSingleton<ICollectionStrategy, EnvironmentCollectionStrategy>();
            services.AddSingleton<ICollectionStrategy, StatusCollectionStrategy>();

            services.AddScoped<IDeviceCommandService, DeviceCommandService>();
            services.AddScoped<IProtocolCommandResolver, ProtocolCommandResolver>();
            services.AddSingleton<SmartMatchingEngine>();
            services.AddSingleton<CommandMappingConfig>();
            services.AddScoped<ICommandExecutor, ModbusCommandExecutor>(); 
            services.AddScoped<IDeviceConnectionManager, DeviceConnectionManager>();
            
            return services;
        }
    }
}
