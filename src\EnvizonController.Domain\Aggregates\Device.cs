using System.ComponentModel.DataAnnotations.Schema;
using EnvizonController.Domain.Common;
using EnvizonController.Shared.Enums;
using ConnectionStatus = EnvizonController.Domain.Enums.ConnectionStatus;
using DeviceStatus = EnvizonController.Domain.Enums.DeviceStatus;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 设备实体
    /// </summary>
    public class Device : BaseEntity<long>
    {
        
        /// <summary>
        /// 私有构造函数，防止直接使用new创建实例
        /// </summary>
        private Device() { }

        /// <summary>
        /// 创建设备实例的工厂方法
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <param name="slaveId">从站地址</param>
        /// <param name="protocolId">协议ID</param>
        /// <param name="connectionType">连接类型</param>
        /// <param name="transportType">传输类型</param>
        /// <returns>新的设备实例</returns>
        public static Device Create(
            string name,
            byte slaveId = 1,
            long protocolId = 0,
            string connectionType = "",
            string transportType = "")
        {
            var device = new Device
            {
                Name = name,
                SlaveId = slaveId,
                ProtocolId = protocolId,
                ConnectionType = connectionType,
                TransportType = transportType,
                CreatedAt = DateTime.Now
            };
            
            return device;
        }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveId { get; set; } = 1;

        /// <summary>
        /// 协议ID
        /// </summary>
        public long ProtocolId { get; set; } 
 
        [NotMapped]
        public Protocol? Protocol { get; set; }
        
        /// <summary>
        /// 连接类型
        /// </summary>
        public string ConnectionType { get; set; } = string.Empty;

        /// <summary>
        /// 传输类型
        /// </summary>
        public string TransportType { get; set; } = string.Empty;

        /// <summary>
        /// 串口名称
        /// </summary>
        public string PortName { get; set; } = string.Empty;

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 校验位
        /// </summary>
        public int Parity { get; set; } = 0;

        /// <summary>
        /// 停止位
        /// </summary>
        public int StopBits { get; set; } = 1;

        /// <summary>
        /// 主机地址（用于网络连接）
        /// </summary>
        public string HostAddress { get; set; } = string.Empty;

        /// <summary>
        /// 端口（用于网络连接）
        /// </summary>
        public int Port { get; set; } = 502;

        /// <summary>
        /// 连接超时（毫秒）
        /// </summary>
        public int ConnectionTimeout { get; set; } = 5000;

        /// <summary>
        /// 读取超时（毫秒）
        /// </summary>
        public int ReadTimeout { get; set; } = 1000;

        /// <summary>
        /// 写入超时（毫秒）
        /// </summary>
        public int WriteTimeout { get; set; } = 1000;

        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus Status { get; set; } = DeviceStatus.Normal;

        /// <summary>
        /// 设备运行状态
        /// </summary>
        public DeviceOperatingStatus OperatingStatus { get; set; } = DeviceOperatingStatus.Unknown;

        /// <summary>
        /// 连接状态
        /// </summary>
        public ConnectionStatus ConnectionStatus { get; set; } = ConnectionStatus.Disconnected;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastUpdatedAt { get; set; }

        /// <summary>
        /// 最后连接时间
        /// </summary>
        public DateTime? LastConnectedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 是否自动启动数据采集任务
        /// </summary>
        public bool AutoStart { get; set; } = false;

        /// <summary>
        /// 数据采集间隔（毫秒）
        /// </summary>
        public int CollectionIntervalMs { get; set; } = 1000;

        public long? TestId { get; set; }
    }
}
