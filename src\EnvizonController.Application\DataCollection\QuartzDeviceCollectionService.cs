using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;

namespace EnvizonController.Application.DataCollection;

/// <summary>
/// Quartz设备数据采集服务
/// </summary>
public class QuartzDeviceCollectionService : IQuartzDeviceCollectionService
{
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<QuartzDeviceCollectionService> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IDataCollectionCoordinator _coordinator;
    private IScheduler? _scheduler;

    public QuartzDeviceCollectionService(
        ISchedulerFactory schedulerFactory,
        ILogger<QuartzDeviceCollectionService> logger,
        IServiceScopeFactory serviceScopeFactory,
        IDataCollectionCoordinator coordinator)
    {
        _schedulerFactory = schedulerFactory;
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _coordinator = coordinator;
    }

    /// <summary>
    /// 启动调度器
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_scheduler != null && _scheduler.IsStarted)
        {
            _logger.LogWarning("调度器已经启动");
            return;
        }

        _scheduler = await _schedulerFactory.GetScheduler(cancellationToken);
        await _scheduler.Start(cancellationToken);
        _logger.LogInformation("Quartz设备数据采集调度器已启动");

        // 加载自动启动的设备
        await LoadAutoStartDevicesAsync();
    }

    /// <summary>
    /// 停止调度器
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_scheduler == null || _scheduler.IsShutdown)
        {
            _logger.LogWarning("调度器未启动或已停止");
            return;
        }

        // 关闭调度器，但不等待任务完成
        await _scheduler.Shutdown(false, cancellationToken);
        _logger.LogInformation("Quartz设备数据采集调度器已停止");
    }

    /// <summary>
    /// 加载自动启动的设备采集任务（启动时运行一次）
    /// </summary>
    private async Task LoadAutoStartDevicesAsync()
    {
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceAppService>();
            var devices = await deviceService.GetAllAutoStartDevicesWithProtocolAsync();

            // 筛选出自动启动的设备
            var autoStartDevices = devices.Where(d => d.AutoStart).ToList();
            _logger.LogInformation("找到 {Count} 个自动启动的设备", autoStartDevices.Count);

            // 为每个设备创建采集任务
            foreach (var device in autoStartDevices )
            {
                await ScheduleDeviceCollectionTask(device);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载自动启动设备失败：{Message}", ex.Message);
        }
    }

    /// <summary>
    /// 为设备安排数据采集任务
    /// </summary>
    public async Task<bool> ScheduleDeviceCollectionTask(Device device)
    {
        if (_scheduler == null || !_scheduler.IsStarted)
        {
            _logger.LogWarning("调度器未启动，无法安排设备 {DeviceId} 的采集任务", device.Id);
            return false;
        }

        try
        {
            // 任务名称和组名
            string jobName = $"DeviceCollectionJob_{device.Id}";
            string jobGroup = "DeviceCollectionGroup";
            string triggerName = $"DeviceCollectionTrigger_{device.Id}";
            string triggerGroup = "DeviceCollectionTriggerGroup";

            // 检查任务是否已存在
            var jobKey = new JobKey(jobName, jobGroup);
            if (await _scheduler.CheckExists(jobKey))
            {
                _logger.LogInformation("设备 {DeviceId} 的采集任务已存在，将更新", device.Id);
                await _scheduler.DeleteJob(jobKey);
            }

            // 创建任务
            var jobDetail = JobBuilder.Create<DeviceDataCollectionJob>()
                .WithIdentity(jobName, jobGroup)
                .UsingJobData(new JobDataMap { { "Device", device } })
                .Build();

            // 创建触发器，间隔时间从设备配置中获取
            var trigger = TriggerBuilder.Create()
                .WithIdentity(triggerName, triggerGroup)
                .StartNow()
                .WithSimpleSchedule(x => x
                    .WithInterval(TimeSpan.FromMilliseconds(device.CollectionIntervalMs))
                    .RepeatForever())
                .Build();

            // 安排任务
            await _scheduler.ScheduleJob(jobDetail, trigger);
            
            // 将设备添加到coordinator的活动设备列表
            await _coordinator.StartDeviceCollectionAsync(device.Id);

            _logger.LogInformation("已为设备 {DeviceId} 安排数据采集任务，间隔：{IntervalMs}毫秒", 
                device.Id, device.CollectionIntervalMs);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为设备 {DeviceId} 安排数据采集任务失败：{Message}", device.Id, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 取消设备数据采集任务
    /// </summary>
    public async Task<bool> UnscheduleDeviceCollectionTask(long deviceId)
    {
        if (_scheduler == null || !_scheduler.IsStarted)
        {
            _logger.LogWarning("调度器未启动，无法取消设备 {DeviceId} 的采集任务", deviceId);
            return false;
        }

        try
        {
            // 任务名称和组名
            string jobName = $"DeviceCollectionJob_{deviceId}";
            string jobGroup = "DeviceCollectionGroup";

            // 取消任务
            var jobKey = new JobKey(jobName, jobGroup);
            bool result = await _scheduler.DeleteJob(jobKey);
            
            // 从coordinator的活动设备列表移除
            await _coordinator.StopDeviceCollectionAsync(deviceId);

            if (result)
                _logger.LogInformation("已取消设备 {DeviceId} 的数据采集任务", deviceId);
            else
                _logger.LogWarning("设备 {DeviceId} 的数据采集任务不存在", deviceId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消设备 {DeviceId} 的数据采集任务失败：{Message}", deviceId, ex.Message);
            return false;
        }
    }
    
    /// <summary>
    /// 更新设备数据采集任务
    /// </summary>
    public async Task<bool> UpdateDeviceCollectionTask(Device device)
    {
        // 先取消现有任务，再重新安排
        await UnscheduleDeviceCollectionTask(device.Id);
        return await ScheduleDeviceCollectionTask(device);
    }
} 