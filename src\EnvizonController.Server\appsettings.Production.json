{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "EnvizonController": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "EnvizonController": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/envizon-server-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "SwaggerUI": {"Enabled": false, "RoutePrefix": "api-docs", "DocumentTitle": "EnvizonController API Documentation (Production)", "EnableTryItOut": false, "DisplayRequestDuration": false, "ShowExtensions": false}}