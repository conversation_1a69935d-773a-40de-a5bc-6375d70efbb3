using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Domain.Services;

/// <summary>
///     测试项服务接口
///     处理测试项相关的业务逻辑
/// </summary>
public interface ITestRunDomainService
{
    /// <summary>
    ///     创建新的测试项
    /// </summary>
    /// <param name="testRun">测试项</param>
    /// <returns>创建的测试项</returns>
    Task<TestRun> CreateTestItemAsync(TestRun testRun);

    /// <summary>
    ///     更新测试项
    /// </summary>
    /// <param name="testRun">测试项</param>
    /// <returns>更新后的测试项</returns>
    Task<TestRun> UpdateTestItemAsync(TestRun testRun);

    /// <summary>
    ///     删除测试项
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>是否成功删除</returns>
    Task<bool> DeleteTestItemAsync(long id);

    /// <summary>
    ///     启动测试
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    Task<TestRun?> StartTestAsync(long id);

    /// <summary>
    ///     停止测试
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    Task<TestRun?> StopTestAsync(long id);

    /// <summary>
    ///     完成测试
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    Task<TestRun?> CompleteTestAsync(long id);

    /// <summary>
    ///     从程式创建测试项
    /// </summary>
    /// <param name="programId">程式ID</param>
    /// <param name="testName">测试名称</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="description">测试描述</param>
    /// <returns>创建的测试项</returns>
    Task<TestRun> CreateTestFromProgramAsync(long programId, string testName, long deviceId, string? description = null);

    /// <summary>
    ///     从程式链接创建测试项
    /// </summary>
    /// <param name="programLinkId">程式链接ID</param>
    /// <param name="testName">测试名称</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="description">测试描述</param>
    /// <returns>创建的测试项</returns>
    Task<TestRun> CreateTestFromProgramLinkAsync(long programLinkId, string testName, long deviceId, string? description = null);

    /// <summary>
    ///     从程式链接创建测试项（支持额外配置）
    /// </summary>
    /// <param name="programLinkId">程式链接ID</param>
    /// <param name="testName">测试名称</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="description">测试描述</param>
    /// <param name="extraConfig">额外配置参数</param>
    /// <returns>创建的测试项</returns>
    Task<TestRun> CreateTestFromProgramLinkAsync(long programLinkId, string testName, long deviceId, string? description, Dictionary<string, object>? extraConfig);

    /// <summary>
    ///     创建新的测试项（同步版本）
    /// </summary>
    /// <param name="testRun">测试项</param>
    /// <returns>创建的测试项</returns>
    TestRun CreateTestItem(TestRun testRun);

    /// <summary>
    ///     更新测试项（同步版本）
    /// </summary>
    /// <param name="testRun">测试项</param>
    /// <returns>更新后的测试项</returns>
    TestRun UpdateTestItem(TestRun testRun);

    /// <summary>
    ///     删除测试项（同步版本）
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>是否成功删除</returns>
    bool DeleteTestItem(long id);

    /// <summary>
    ///     启动测试（同步版本）
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    TestRun? StartTest(long id);

    /// <summary>
    ///     停止测试（同步版本）
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    TestRun? StopTest(long id);

    /// <summary>
    ///     完成测试（同步版本）
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项</returns>
    TestRun? CompleteTest(long id);

    /// <summary>
    ///     从程式创建测试项（同步版本）
    /// </summary>
    /// <param name="programId">程式ID</param>
    /// <param name="testName">测试名称</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="description">测试描述</param>
    /// <returns>创建的测试项</returns>
    TestRun CreateTestFromProgram(long programId, string testName, long deviceId, string? description = null);

    /// <summary>
    ///     从程式链接创建测试项（同步版本）
    /// </summary>
    /// <param name="programLinkId">程式链接ID</param>
    /// <param name="testName">测试名称</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="description">测试描述</param>
    /// <returns>创建的测试项</returns>
    TestRun CreateTestFromProgramLink(long programLinkId, string testName, long deviceId, string? description = null);
}
