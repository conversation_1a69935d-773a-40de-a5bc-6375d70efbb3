using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 程式表API服务接口
    /// </summary>
    public interface IProgramApiService : IApiService
    {
        /// <summary>
        /// 获取所有程式表
        /// </summary>
        Task<Result<PagedResultDto<ProgramDTO>>> GetProgramsAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取程式表
        /// </summary>
        Task<Result<ProgramDTO>> GetProgramAsync(long id);

        /// <summary>
        /// 按名称获取程式表
        /// </summary>
        Task<Result<ProgramDTO>> GetProgramByNameAsync(string name);

        /// <summary>
        /// 创建程式表
        /// </summary>
        Task<Result<ProgramDTO>> CreateProgramAsync(ProgramDTO program);

        /// <summary>
        /// 更新程式表
        /// </summary>
        Task<Result<ProgramDTO>> UpdateProgramAsync(ProgramDTO program);

        /// <summary>
        /// 删除程式表
        /// </summary>
        Task<Result<bool>> DeleteProgramAsync(long id);

        /// <summary>
        /// 获取程式表的所有步骤
        /// </summary>
        Task<Result<List<ProgramStepDTO>>> GetProgramStepsAsync(long programId);

        /// <summary>
        /// 创建程式步骤
        /// </summary>
        Task<Result<ProgramStepDTO>> CreateProgramStepAsync(ProgramStepDTO step);

        /// <summary>
        /// 更新程式步骤
        /// </summary>
        Task<Result<ProgramStepDTO>> UpdateProgramStepAsync(ProgramStepDTO step);

        /// <summary>
        /// 删除程式步骤
        /// </summary>
        Task<Result<bool>> DeleteProgramStepAsync(long id);

        /// <summary>
        /// 批量更新程式步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <param name="steps">要更新的步骤列表</param>
        /// <returns>更新后的步骤列表</returns>
        Task<Result<List<ProgramStepDTO>>> BatchUpdateProgramStepsAsync(long programId, List<ProgramStepDTO> steps);
    }
}