# EnvizonController 日志系统架构设计方案

## 1. 项目概述与分析

### 1.1 项目架构分析

EnvizonController 采用经典的分层架构模式，具有良好的模块化设计：

- **Domain层**：核心业务逻辑和领域模型
- **Application层**：应用服务和业务编排  
- **Infrastructure层**：数据持久化和外部服务
- **Presentation层**：Avalonia UI界面
- **Server层**：Web API服务
- **Shared层**：共享DTOs和枚举
- **Modbus模块**：设备通信抽象层

### 1.2 现有技术栈

- **依赖注入**：Microsoft.Extensions.DependencyInjection
- **日志框架**：Serilog (控制台 + 文件输出)
- **数据持久化**：Entity Framework Core + SQLite
- **映射框架**：AutoMapper
- **后台服务**：IHostedService + Quartz
- **多平台支持**：Avalonia UI

### 1.3 日志系统需求分析

基于需求调研，确定以下关键要求：

1. **存储策略**：统一存储，通过日志类型字段区分
2. **客户端上传**：用户操作行为、设备测试操作、界面交互记录、异常错误信息
3. **查询用途**：运维监控、审计追踪、故障排查、用户行为分析
4. **性能要求**：优先保证系统稳定性，日志延迟可接受在秒级，支持异步批量处理
5. **保留策略**：操作日志保留1年，异常日志保留3个月，提供手动和自动清理功能

## 2. 整体架构设计

### 2.1 架构总览

```mermaid
graph TB
    subgraph "客户端层 (Presentation)"
        UI[Avalonia UI]
        ClientLogger[客户端日志收集器]
    end
    
    subgraph "应用服务层 (Application)"
        LogAppService[日志应用服务]
        LogUploadService[日志上传服务]
        LogQueryService[日志查询服务]
    end
    
    subgraph "日志核心层 (Logging Domain)"
        LogAggregateRoot[日志聚合根]
        LogFactory[日志工厂]
        LogEnrichers[日志增强器]
    end
    
    subgraph "基础设施层 (Infrastructure)"
        LogWriters[日志写入器]
        LogRepository[日志仓储]
        LogRetentionService[日志保留策略服务]
    end
    
    subgraph "存储层"
        Database[(SQLite数据库)]
