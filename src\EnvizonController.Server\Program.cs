using EnvizonController.Application;
using EnvizonController.Application.DataCollection;
using EnvizonController.Application.Extensions;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using EnvizonController.Application.Services;
using EnvizonController.Domain;
using EnvizonController.Infrastructure;
using EnvizonController.Infrastructure.Persistence;
using EnvizonController.Mqtt.Server.DependencyInjection;
using EnvizonController.Server.Configuration;
using Microsoft.OpenApi.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 1. 添加配置源
builder.Configuration.AddJsonFile("appsettings.json", true, true);

// 2. 配置Serilog - 增强控制台日志显示
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .Enrich.WithMachineName()
    .Enrich.WithThreadId()
    .CreateLogger();

builder.Host.UseSerilog();

// 记录启动日志
Log.Information("=== EnvizonController Server 正在启动 ===");
Log.Information("环境: {Environment}", builder.Environment.EnvironmentName);
Log.Information("应用程序名称: {ApplicationName}", builder.Environment.ApplicationName);

// 3. 注册服务
var services = builder.Services;
var configuration = builder.Configuration;

// 添加配置和日志服务
services.AddSingleton<IConfiguration>(configuration);
services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));
services.AddSingleton(Log.Logger);

// 绑定SwaggerUI配置
var swaggerOptions = new SwaggerUIOptions();
configuration.GetSection(SwaggerUIOptions.SectionName).Bind(swaggerOptions);
services.Configure<SwaggerUIOptions>(configuration.GetSection(SwaggerUIOptions.SectionName));

Log.Information("SwaggerUI配置: 启用={Enabled}, 路由前缀='{RoutePrefix}', 标题='{Title}'", 
    swaggerOptions.Enabled, swaggerOptions.RoutePrefix, swaggerOptions.DocumentTitle);

// 添加API相关服务
services.AddControllers();
services.AddEndpointsApiExplorer();

// 条件性添加Swagger服务
if (swaggerOptions.Enabled)
{
    Log.Information("正在配置Swagger服务...");
    services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "EnvizonController API",
            Version = "v1",
            Description = "EnvizonController系统的API接口",
            Contact = new OpenApiContact
            {
                Name = "EnvizonController Team",
                Email = "<EMAIL>"
            }
        });

        // 添加JWT认证支持
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            }
        });

        // 启用XML注释（如果存在）
        var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
        var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
        if (File.Exists(xmlPath))
        {
            c.IncludeXmlComments(xmlPath);
            Log.Information("已加载XML文档注释: {XmlPath}", xmlPath);
        }
    });
}
else
{
    Log.Information("SwaggerUI已禁用");
}

// 添加CORS策略
services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

Log.Information("正在注册应用服务...");

// 添加应用层服务
services.AddApplicationServices();
services.AddInfrastructureServices(configuration);
services.AddDomainServices();

// 手动注册 ProtocolAppService
services.AddScoped<IProtocolAppService, ProtocolAppService>();

// 添加 MQTT 服务
Log.Information("正在配置MQTT服务...");
services.AddMqttServer();
//services.AddMqttClient(options =>
//{
//    // 根据服务器需要调整 MQTT 客户端配置
//    options.WithClientId("EnvizonServerMqttClient") // 使用不同的客户端 ID
//        .WithTcpServer("localhost", 1883) // 或者从配置读取地址
//        .WithCleanSession();
//});

// 添加 Quartz 设备数据采集服务
Log.Information("正在配置数据采集服务...");
services.AddQuartzDeviceCollection();

// 将 QuartzDeviceCollectionBackgroundService 注册为托管服务（替换原来的 DataCollectionBackgroundService）
services.AddHostedService<QuartzDeviceCollectionBackgroundService>();

var app = builder.Build();

Log.Information("应用程序构建完成，开始初始化...");

// 4. 初始化默认协议和设备 (在应用构建之后)
using (var scope = app.Services.CreateScope())
{
    try
    {
        Log.Information("正在初始化数据库...");
        // 确保数据库和表已创建
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        dbContext.Database.EnsureCreated();
        // 或者使用迁移 
        // dbContext.Database.Migrate();

        Log.Information("正在初始化默认协议...");
        // 初始化默认协议
        var protocolAppService = scope.ServiceProvider.GetRequiredService<IProtocolAppService>();
        protocolAppService.InitializeDefaultProtocols();

        Log.Information("正在初始化默认程序...");
        var programAppService = scope.ServiceProvider.GetRequiredService<IProgramAppService>();
        programAppService.InitializeDefaultProgramsAsync().Wait();

        Log.Information("正在初始化默认设备...");
        // 初始化默认设备
        var deviceAppService = scope.ServiceProvider.GetRequiredService<IDeviceAppService>();
        deviceAppService.InitializeDefaultDeviceAsync().Wait();

        Log.Information("正在初始化默认测试项...");
        // 初始化默认测试项
        var testItemAppService = scope.ServiceProvider.GetRequiredService<ITestItemAppService>();
        testItemAppService.InitializeDefaultTestItemsAsync().Wait();

        Log.Information("数据初始化完成");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "初始化过程中发生错误");
        throw;
    }
}

Log.Information("正在配置HTTP请求管道...");

// 配置 HTTP 请求管道
// 条件性启用Swagger UI
if (swaggerOptions.Enabled)
{
    Log.Information("启用Swagger中间件...");
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", swaggerOptions.DocumentTitle);
        c.RoutePrefix = swaggerOptions.RoutePrefix;
        c.DocumentTitle = swaggerOptions.DocumentTitle;
        
        if (swaggerOptions.EnableTryItOut)
        {
            c.EnableTryItOutByDefault();
        }
        
        if (swaggerOptions.DisplayRequestDuration)
        {
            c.DisplayRequestDuration();
        }
        
        // 自定义CSS样式
        c.InjectStylesheet("/swagger-ui/custom.css");
    });
    
    // 添加自定义CSS端点
    app.MapGet("/swagger-ui/custom.css", () => Results.Content(@"
        .swagger-ui .topbar { display: none; }
        .swagger-ui .info .title { color: #3b82f6; }
        .swagger-ui .scheme-container { background: #f8fafc; padding: 10px; border-radius: 4px; }
    ", "text/css"));

    Log.Information("SwaggerUI已启用，访问地址: {SwaggerUrl}", 
        swaggerOptions.RoutePrefix == string.Empty ? "/" : $"/{swaggerOptions.RoutePrefix}");
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// 注释掉认证暂时不使用
// app.UseAuthentication();
// app.UseAuthorization();

// 配置默认欢迎页和API路由
app.MapGet("/", () => 
{
    var welcomeMessage = "Welcome to EnvizonController API Server!";
    if (swaggerOptions.Enabled)
    {
        welcomeMessage += $"\n\nAPI文档地址: {(swaggerOptions.RoutePrefix == string.Empty ? "/" : $"/{swaggerOptions.RoutePrefix}")}";
    }
    Log.Information("访问根路径");
    return welcomeMessage;
});

app.MapControllers();

// 添加应用程序生命周期事件
app.Lifetime.ApplicationStarted.Register(() =>
{
    Log.Information("=== EnvizonController Server 启动完成 ===");
    Log.Information("服务器地址: {Urls}", string.Join(", ", app.Urls));
    if (swaggerOptions.Enabled)
    {
        Log.Information("SwaggerUI地址: {SwaggerUrl}", 
            app.Urls.FirstOrDefault() + (swaggerOptions.RoutePrefix == string.Empty ? "" : swaggerOptions.RoutePrefix));
    }
});

app.Lifetime.ApplicationStopping.Register(() =>
{
    Log.Information("=== EnvizonController Server 正在停止 ===");
});

app.Lifetime.ApplicationStopped.Register(() =>
{
    Log.Information("=== EnvizonController Server 已停止 ===");
    Log.CloseAndFlush();
});

try
{
    Log.Information("启动EnvizonController Server...");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "应用程序启动失败");
    throw;
}
finally
{
    Log.CloseAndFlush();
}