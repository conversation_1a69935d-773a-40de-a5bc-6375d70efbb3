﻿using System.Globalization;
using Avalonia.Data.Converters;

namespace EnvizonController.Presentation.Converters
{
    public class PercentageToWidthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double progress && parameter is string max)
            {
                if (double.TryParse(max, out double maximum))
                {
                    return (progress / maximum) * 100; // Assuming the ProgressBar's parent width is 100 units } } return 0; }

                }

            }
            return 0;
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}