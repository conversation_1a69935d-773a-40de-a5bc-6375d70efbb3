# EnvizonController Server - SwaggerUI 配置说明

## 概述

EnvizonController Server 现在支持可配置的 SwaggerUI 功能，可以根据不同环境的需要启用或禁用 API 文档界面。

## 配置选项

在 `appsettings.json` 或环境特定的配置文件中，可以配置以下 SwaggerUI 选项：

```json
{
  "SwaggerUI": {
    "Enabled": true,                    // 是否启用SwaggerUI
    "RoutePrefix": "",                  // 路由前缀，空字符串表示根路径
    "DocumentTitle": "EnvizonController API Documentation", // 文档标题
    "EnableTryItOut": true,             // 是否启用"Try it out"功能
    "DisplayRequestDuration": true,     // 是否显示请求持续时间
    "ShowExtensions": true              // 是否显示扩展信息
  }
}
```

## 环境配置

### 开发环境 (Development)
- SwaggerUI 默认启用
- 详细的调试日志
- 完整的 API 交互功能

### 生产环境 (Production)
- SwaggerUI 默认禁用（安全考虑）
- 简化的日志输出
- 如需启用，建议设置访问路径前缀

## 日志配置

服务器现在支持增强的日志记录功能：

### 控制台日志
- 彩色输出（在支持的终端中）
- 结构化日志格式
- 实时显示服务器状态

### 文件日志
- 自动按日期滚动
- 保留指定天数的日志文件
- 详细的错误堆栈信息

### 日志级别
- `Debug`: 详细的调试信息
- `Information`: 一般信息
- `Warning`: 警告信息
- `Error`: 错误信息
- `Fatal`: 致命错误

## 使用示例

### 启用 SwaggerUI（开发环境）
```json
{
  "SwaggerUI": {
    "Enabled": true,
    "RoutePrefix": "",
    "DocumentTitle": "EnvizonController API - Development"
  }
}
```

访问地址：`http://localhost:5000/`

### 禁用 SwaggerUI（生产环境）
```json
{
  "SwaggerUI": {
    "Enabled": false
  }
}
```

### 自定义路径的 SwaggerUI
```json
{
  "SwaggerUI": {
    "Enabled": true,
    "RoutePrefix": "api-docs",
    "DocumentTitle": "EnvizonController API Documentation"
  }
}
```

访问地址：`http://localhost:5000/api-docs`

## 安全建议

1. **生产环境**：建议禁用 SwaggerUI 或设置访问控制
2. **内网环境**：可以启用但建议使用非根路径
3. **公网环境**：强烈建议禁用或添加身份验证

## 启动日志示例

启动时，控制台会显示详细的初始化信息：

```
[2024-01-15 10:30:00.123] [INF] === EnvizonController Server 正在启动 ===
[2024-01-15 10:30:00.124] [INF] 环境: Development
[2024-01-15 10:30:00.125] [INF] SwaggerUI配置: 启用=True, 路由前缀='', 标题='EnvizonController API Documentation (Development)'
[2024-01-15 10:30:00.126] [INF] 正在配置Swagger服务...
[2024-01-15 10:30:00.200] [INF] 正在注册应用服务...
[2024-01-15 10:30:00.300] [INF] 正在配置MQTT服务...
[2024-01-15 10:30:00.400] [INF] 正在配置数据采集服务...
[2024-01-15 10:30:01.000] [INF] === EnvizonController Server 启动完成 ===
[2024-01-15 10:30:01.001] [INF] 服务器地址: http://localhost:5000
[2024-01-15 10:30:01.002] [INF] SwaggerUI地址: http://localhost:5000
```

## 故障排除

### SwaggerUI 无法访问
1. 检查 `Enabled` 配置是否为 `true`
2. 确认 `RoutePrefix` 配置正确
3. 查看启动日志中的 SwaggerUI 地址

### 日志不显示
1. 检查 Serilog 配置
2. 确认日志级别设置
3. 验证日志文件路径权限

### 性能问题
1. 生产环境禁用 SwaggerUI
2. 调整日志级别为 `Information` 或更高
3. 限制日志文件保留天数 