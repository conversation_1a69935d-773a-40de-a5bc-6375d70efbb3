{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "EnvizonController": "Debug"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "EnvizonController": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/envizon-server-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=envizon.db"}, "DataCollection": {"Buffer": {"EnableBatchProcessing": true, "ProcessingIntervalSeconds": 2, "BatchSize": 100, "AlarmProcessingIntervalSeconds": 1}}, "SwaggerUI": {"Enabled": true, "RoutePrefix": "", "DocumentTitle": "EnvizonController API Documentation", "EnableTryItOut": true, "DisplayRequestDuration": true, "ShowExtensions": true}}