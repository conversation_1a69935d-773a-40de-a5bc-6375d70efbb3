using AutoMapper;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Application.Mapping
{
    /// <summary>
    /// TestRun 领域对象的 AutoMapper 配置文件
    /// </summary>
    public class TestItemMappingProfile : Profile
    {
        public TestItemMappingProfile()
        {
            // 配置 TestRun -> TestRunDTO 的映射
            CreateMap<TestRun, TestRunDTO>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.TestSteps, opt => opt.Ignore())
                .ForMember(dest => dest.ExecutionId, opt => opt.Ignore())
                .ForMember(dest => dest.ExecutionConfigJson, opt => opt.MapFrom(src => src.ExecutionConfigJson))
                ;
                
                
            // 配置 TestRunDTO -> TestRun 的映射
            CreateMap<TestRunDTO, TestRun>()
                .ForMember(dest => dest.Device, opt => opt.Ignore())
                .ForMember(dest => dest.DataCollections, opt => opt.Ignore())
                .ForMember(dest => dest.ActualDurationSeconds, opt => opt.Ignore())
                .ForMember(dest => dest.EstimatedDurationSeconds, opt => opt.Ignore())
                .ForMember(dest => dest.ExecutionConfigJson, opt => opt.Ignore())
                .ForMember(dest => dest.ExecutionType, opt => opt.Ignore());

            //Type
            //    OriginalId
            //SnapshotMetadata
            // 快照映射
            CreateMap<ProgramSnapshot, ProgramSnapshotDTO>();
            CreateMap<ProgramSnapshotDTO, ProgramSnapshot>()
                .ForMember(dest => dest.SnapshotMetadata, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.Ignore())
                .ForMember(dest => dest.OriginalId, opt => opt.Ignore());

            CreateMap<ProgramLinkSnapshot, ProgramLinkSnapshotDTO>();
            CreateMap<ProgramLinkSnapshotDTO, ProgramLinkSnapshot>()
                .ForMember(dest => dest.SnapshotMetadata, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.Ignore())
                .ForMember(dest => dest.OriginalId, opt => opt.Ignore());
            
                
            
            CreateMap<ProgramLinkItemSnapshot, ProgramLinkItemSnapshotDTO>();
            CreateMap<ProgramLinkItemSnapshotDTO, ProgramLinkItemSnapshot>();

            CreateMap<StepSnapshot, StepSnapshotDTO>();
            CreateMap<StepSnapshotDTO, StepSnapshot>();
        }
    }
}